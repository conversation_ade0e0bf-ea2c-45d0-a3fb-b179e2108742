import React, { useEffect, useState, useRef } from 'react';
import { connect, Room, LocalAudioTrack } from 'livekit-client';
import './App.css';

const LIVEKIT_URL = 'wss://your-livekit-server.com'; // Replace with your livekit server URL
const LIVEKIT_TOKEN = 'your_access_token'; // Replace with your livekit access token
const BACKEND_API_URL = 'http://localhost:8000'; // Replace with your backend API URL

function App() {
  const [room, setRoom] = useState<Room | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [response, setResponse] = useState('');
  const recognitionRef = useRef<SpeechRecognition | null>(null);

  useEffect(() => {
    // Initialize Speech Recognition
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (!SpeechRecognition) {
      alert('Speech Recognition API not supported in this browser.');
      return;
    }
    const recognition = new SpeechRecognition();
    recognition.lang = 'en-US';
    recognition.interimResults = false;
    recognition.maxAlternatives = 1;

    recognition.onresult = (event: SpeechRecognitionEvent) => {
      const speechResult = event.results[0][0].transcript;
      setTranscript(speechResult);
      sendCommandToBackend(speechResult);
    };

    recognition.onerror = (event: any) => {
      console.error('Speech recognition error', event.error);
    };

    recognitionRef.current = recognition;
  }, []);

  const startRecognition = () => {
    recognitionRef.current?.start();
  };

  const sendCommandToBackend = async (command: string) => {
    try {
      const response = await fetch(`${BACKEND_API_URL}/trigger-workflow/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ command }),
      });
      const data = await response.json();
      setResponse(data.response || 'No response from backend');
      speakResponse(data.response || 'No response from backend');
    } catch (error) {
      console.error('Error sending command to backend', error);
      setResponse('Error communicating with backend');
    }
  };

  const speakResponse = (text: string) => {
    if (!window.speechSynthesis) {
      console.warn('Speech Synthesis not supported');
      return;
    }
    const utterance = new SpeechSynthesisUtterance(text);
    window.speechSynthesis.speak(utterance);
  };

  const connectToLivekit = async () => {
    try {
      const room = await connect(LIVEKIT_URL, LIVEKIT_TOKEN, {
        audio: true,
        video: false,
      });
      setRoom(room);
      setIsConnected(true);

      // Publish local audio track
      const audioTrack = await LocalAudioTrack.create();
      await room.localParticipant.publishTrack(audioTrack);

      room.on('disconnected', () => {
        setIsConnected(false);
        setRoom(null);
      });
    } catch (error) {
      console.error('Failed to connect to LiveKit', error);
    }
  };

  const disconnectFromLivekit = () => {
    room?.disconnect();
    setIsConnected(false);
    setRoom(null);
  };

  return (
    <div className="App">
      <h1>Jarvis Assistant</h1>
      <div>
        {!isConnected ? (
          <button onClick={connectToLivekit}>Connect to LiveKit</button>
        ) : (
          <button onClick={disconnectFromLivekit}>Disconnect LiveKit</button>
        )}
      </div>
      <div>
        <button onClick={startRecognition}>Start Voice Command</button>
      </div>
      <div>
        <h2>Transcript:</h2>
        <p>{transcript}</p>
      </div>
      <div>
        <h2>Response:</h2>
        <p>{response}</p>
      </div>
    </div>
  );
}

export default App;
